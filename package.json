{"name": "noti", "version": "1.0.0", "main": "dist-electron/main/index.js", "description": "Smart Note-Taking & Study Companion", "author": "Noti Team", "license": "UNLICENSED", "private": true, "keywords": ["electron", "vite", "vue3", "note-taking", "study", "books"], "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:3344/"}}, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build && electron-builder", "build:all": "vue-tsc --noEmit && vite build && electron-builder -mwl", "build:win": "vue-tsc --noEmit && vite build && electron-builder --win", "build:mac": "vue-tsc --noEmit && vite build && electron-builder --mac", "build:linux": "vue-tsc --noEmit && vite build && electron-builder --linux", "postinstall": "electron-rebuild -f -w sqlite3", "electron-rebuild": "electron-rebuild -f -w sqlite3", "preview": "vite preview", "test:db": "node --loader ts-node/esm electron/main/database/db-test-utils.ts", "populate-test-data": "node --loader ts-node/esm scripts/populateTestData.ts", "populate-real-db": "node --loader ts-node/esm scripts/populateTestData.ts --use-real-db", "dump-db": "node scripts/dumpDatabaseContents.cjs", "dump-db-simple": "node scripts/dumpDatabaseContents.cjs"}, "devDependencies": {"@types/node": "^22.15.3", "@types/sqlite3": "^5.1.0", "@vitejs/plugin-vue": "^5.0.4", "electron": "^29.1.1", "electron-builder": "^24.13.3", "electron-icon-builder": "^2.0.1", "electron-rebuild": "^3.2.9", "rollup": "^4.43.0", "ts-node": "^10.9.2", "typescript": "^5.4.2", "vite": "^6.3.5", "vite-plugin-electron": "^0.28.4", "vite-plugin-electron-renderer": "^0.14.5", "vue": "^3.4.21", "vue-tsc": "^2.2.10"}, "dependencies": {"@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-focus": "^2.12.0", "@tiptap/extension-font-family": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-mention": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-typography": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/extension-youtube": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/vue-3": "^2.12.0", "@types/axios": "^0.14.4", "@types/dompurify": "^3.2.0", "archiver": "^5.3.1", "axios": "^1.9.0", "chart.js": "^4.4.9", "chokidar": "^3.5.3", "discord-rpc": "^4.0.1", "dompurify": "^3.2.6", "fs-extra": "^11.3.0", "fuse.js": "^7.0.0", "got": "^13.0.0", "iso-639-3": "^3.0.1", "markdown-it": "^14.1.0", "node-cache": "^5.1.2", "pinia": "^3.0.3", "puppeteer": "^24.10.0", "sqlite3": "^5.1.7", "tiptap-extension-resize-image": "^1.2.2", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.0"}}