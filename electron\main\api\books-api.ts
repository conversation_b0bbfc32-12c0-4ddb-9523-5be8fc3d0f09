// Books API - Specialized functions for book operations including OpenLibrary integration
import {
  createBook,
  getAllBooks,
  getAllBooksWithNoteCounts,
  getBookById,
  getBookByIsbn,
  getBookByOlid,
  searchBooks as searchBooksInDb,
  getRecentBooks,
  updateBook,
  deleteBook as deleteBookFromDb, // Aliased original deleteBook
  Book,
  BookWithNoteCount,
  dbAll,
  getNotesByFolderId, // Added for new delete logic
  updateNote,         // Added for new delete logic
  deleteFolder as deleteFolderFromDb // Added for folder deletion when empty
} from '../database/database-api';
import { saveBookCover, getBookCover, filePathToMediaUrl } from './media-api';
import {
  ensureBooksRootFolder,
  getBookFolder,
  updateFolderWithValidation, // Added for new delete logic
  createFolderWithValidation  // Use this instead of createFolder for proper backup events
} from './folders-api';
import axios from 'axios';
import * as http from 'node:http';
import * as https from 'node:https';
import NodeCache from 'node-cache';
import { convertLanguageCode } from '../../utils/language-converter';
import { sanitizeBookTitle } from '../../utils/filename-sanitizer';
import { notifyBookChange } from '../database/database-hooks';

// OpenLibrary API interfaces
export interface OpenLibrarySearchResult {
  title: string;
  author_name?: string[];
  isbn?: string[];
  cover_i?: number;
  cover_edition_key?: string;
  first_publish_year?: number;
  language?: string[];
  edition_count?: number;
  key?: string;
  subject?: string[];
  publisher?: string[];
  publish_year?: number[];
  oclc?: string[];
  lccn?: string[];
  olid?: string;
}

export interface OpenLibraryWorkDetails {
  title: string;
  authors?: Array<{
    author: {
      key: string;
    };
    type: {
      key: string;
    };
  }>;
  description?: string | {
    type: string;
    value: string;
  };
  subjects?: string[];
  first_publish_date?: string;
  key: string;
  covers?: number[];
}

export interface OpenLibraryAuthorDetails {
  name: string;
  key: string;
}

export interface OpenLibraryEditionDetails {
  title: string;
  authors?: Array<{
    key: string;
  }>;
  isbn_10?: string[];
  isbn_13?: string[];
  number_of_pages?: number;
  publish_date?: string;
  publishers?: string[];
  covers?: number[];
  languages?: Array<{
    key: string;
  }>;
  description?: string | {
    type: string;
    value: string;
  };
  key: string;
}

// Enhanced book interface for frontend communication with relevance scoring
export interface BookSearchResult extends OpenLibrarySearchResult {
  cover_url?: string;
  description?: string;
  genres?: string;
  isbn_primary?: string;
  relevanceScore?: number;
  hasCustomCover?: boolean;
  userModifiedCover?: boolean;
  preventCoverOverride?: boolean;
}

// Cache for search results to avoid redundant API calls
const searchCache = new NodeCache({
  stdTTL: 5 * 60, // 5 minutes
  checkperiod: 60, // Check for expired items every minute
  useClones: false // Performance optimization for our use case
});

// Helper function to get OpenLibrary cover URL
const getOpenLibraryCoverUrl = (coverKey: number, size: 'S' | 'M' | 'L' = 'L'): string => {
  return `https://covers.openlibrary.org/b/id/${coverKey}-${size}.jpg`;
};

// Helper function to download cover image as binary data
const downloadCoverImageData = async (coverUrl: string): Promise<Buffer> => {
  const downloadWithRedirects = (url: string, redirectCount = 0): Promise<Buffer> => {
    const maxRedirects = 5;

    if (redirectCount > maxRedirects) {
      throw new Error('Too many redirects');
    }

    return new Promise((resolve, reject) => {
      const protocol = url.startsWith('https:') ? https : http;

      const req = protocol.get(url, {
        timeout: 30000, // Increased timeout to 30 seconds
        headers: {
          'User-Agent': 'Noti/1.0 (https://github.com/noti-app/noti)',
          'Accept': 'image/*,*/*;q=0.8'
        }
      }, (res: any) => {
        // Handle redirects
        if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 303 || res.statusCode === 307 || res.statusCode === 308) {
          const location = res.headers.location;
          if (location) {
            console.log(`Cover download redirected to: ${location}`);
            downloadWithRedirects(location, redirectCount + 1)
              .then(resolve)
              .catch(reject);
            return;
          }
        }

        if (res.statusCode !== 200) {
          reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
          return;
        }

        const data: Buffer[] = [];
        res.on('data', (chunk: Buffer) => data.push(chunk));
        res.on('end', () => {
          const buffer = Buffer.concat(data);
          if (buffer.length === 0) {
            reject(new Error('Empty response'));
            return;
          }
          resolve(buffer);
        });
        res.on('error', reject);
      });

      req.on('error', (err) => {
        console.error('Request error:', err);
        reject(err);
      });
      req.on('timeout', () => {
        console.error('Request timeout');
        req.destroy();
        reject(new Error('Cover download timeout'));
      });
    });
  };

  try {
    return await downloadWithRedirects(coverUrl);
  } catch (error) {
    console.error('Error downloading cover image:', error);
    throw new Error('Failed to download cover image');
  }
};

/**
 * Determines if a string is likely an ISBN (10 or 13 digits)
 * @param query - The search query
 * @returns True if the query appears to be an ISBN
 */
const isISBN = (query: string): boolean => {
  // Remove any hyphens or spaces that might be in an ISBN
  const cleanQuery = query.replace(/[-\s]/g, '');

  // ISBN-10: 10 digits (can end with X)
  const isISBN10 = /^(\d{9}[\dX])$/.test(cleanQuery);

  // ISBN-13: 13 digits (always starts with 978 or 979)
  const isISBN13 = /^(978|979)\d{10}$/.test(cleanQuery);

  return isISBN10 || isISBN13;
};

/**
 * Simplified edit distance calculation (optimized)
 * @param str1 - First string
 * @param str2 - Second string
 * @returns Edit distance
 */
const getEditDistance = (str1: string, str2: string): number => {
  const costs = [];
  for (let i = 0; i <= str2.length; i++) {
    let lastValue = i;
    for (let j = 0; j <= str1.length; j++) {
      if (i === 0) {
        costs[j] = j;
      } else {
        if (j > 0) {
          let newValue = costs[j - 1];
          if (str1.charAt(j - 1) !== str2.charAt(i - 1)) {
            newValue = Math.min(Math.min(newValue, lastValue), costs[j]) + 1;
          }
          costs[j - 1] = lastValue;
          lastValue = newValue;
        }
      }
    }
    if (i > 0) costs[str2.length] = lastValue;
  }
  return costs[str2.length];
};

/**
 * Enhanced string similarity check using multiple algorithms
 * Combines character overlap with position awareness for better accuracy
 * @param str1 - First string
 * @param str2 - Second string
 * @returns Similarity score between 0 and 1
 */
export const getEnhancedStringSimilarity = (str1: string, str2: string): number => {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  // Quick exact match check
  if (str1 === str2) return 1.0;

  // Check for substring matches (common in book titles)
  if (longer.includes(shorter)) {
    return 0.85 + (shorter.length / longer.length) * 0.15;
  }

  // Use edit distance with position weighting
  const editDistance = getEditDistance(longer, shorter);
  const basicSimilarity = (longer.length - editDistance) / longer.length;

  // Bonus for common prefixes/suffixes (book series, etc.)
  let positionBonus = 0;
  const minLen = Math.min(str1.length, str2.length);

  // Common prefix bonus
  let prefixLen = 0;
  for (let i = 0; i < minLen && str1[i] === str2[i]; i++) {
    prefixLen++;
  }
  if (prefixLen > 2) {
    positionBonus += (prefixLen / minLen) * 0.1;
  }

  // Common suffix bonus (less important than prefix)
  let suffixLen = 0;
  for (let i = 1; i <= minLen && str1[str1.length - i] === str2[str2.length - i]; i++) {
    suffixLen++;
  }
  if (suffixLen > 2) {
    positionBonus += (suffixLen / minLen) * 0.05;
  }

  return Math.min(basicSimilarity + positionBonus, 1.0);
};

/**
 * Advanced word similarity check with better fuzzy matching for book titles
 * @param word1 - First word
 * @param word2 - Second word
 * @returns True if words are similar enough
 */
const isAdvancedWordSimilar = (word1: string, word2: string): boolean => {
  if (word1.length <= 2 || word2.length <= 2) {
    return word1 === word2; // Exact match for short words
  }

  // Quick checks first
  if (word1 === word2) return true;
  if (word1.includes(word2) || word2.includes(word1)) return true;

  // Common book abbreviations and variations
  const abbreviationMap = new Map([
    ['dr', 'doctor'], ['mr', 'mister'], ['mrs', 'misses'], ['ms', 'miss'],
    ['vol', 'volume'], ['pt', 'part'], ['ed', 'edition'], ['rev', 'revised'],
    ['&', 'and'], ['w/', 'with'], ['w/o', 'without']
  ]);

  const norm1 = word1.toLowerCase();
  const norm2 = word2.toLowerCase();

  // Check abbreviation expansions
  if (abbreviationMap.has(norm1) && abbreviationMap.get(norm1) === norm2) return true;
  if (abbreviationMap.has(norm2) && abbreviationMap.get(norm2) === norm1) return true;

  // Enhanced similarity threshold based on word length
  const minLength = Math.min(word1.length, word2.length);
  const maxLength = Math.max(word1.length, word2.length);

  // Stricter thresholds for longer words
  let threshold = 0.7;
  if (minLength >= 6) threshold = 0.8;
  if (minLength >= 10) threshold = 0.85;

  // More lenient for very different lengths (might be abbreviations)
  if (maxLength / minLength > 2) threshold = 0.6;

  const similarity = getEnhancedStringSimilarity(norm1, norm2);
  return similarity >= threshold;
};

/**
 * Preprocess query to improve fuzzy matching
 * @param query - Original query
 * @returns Processed query variations
 */
const preprocessQuery = (query: string): string[] => {
  const variations = [query];
  const words = query.toLowerCase().split(/\s+/);

  // Common typo corrections
  const typoMap: Record<string, string> = {
    'lrod': 'lord',
    'teh': 'the',
    'adn': 'and',
    'fo': 'of',
    'taht': 'that',
    'hte': 'the'
  };

  // Apply typo corrections
  const correctedWords = words.map(word => typoMap[word] || word);
  if (correctedWords.join(' ') !== query.toLowerCase()) {
    variations.push(correctedWords.join(' '));
  }

  return variations;
};

/**
 * Enhanced relevance scoring algorithm with cover image prioritization
 * Based on research from library science and search engine optimization
 * @param book - OpenLibrary search result
 * @param queries - Array of query variations
 * @param isISBNQuery - Whether the search is for an ISBN
 * @param localBooks - Array of books already in local database (for deduplication scoring)
 * @returns Relevance score (higher = more relevant)
 */
const calculateEnhancedRelevanceScore = (
  book: OpenLibrarySearchResult,
  queries: string[],
  isISBNQuery: boolean,
  localBooks: Book[] = []
): number => {
  let relevanceScore = 0;
  const title = (book.title || '').toLowerCase();
  const authors = book.author_name ? book.author_name.map(a => a.toLowerCase()).join(' ') : '';

  // ISBN exact match gets highest priority (unchanged)
  if (isISBNQuery && book.isbn && Array.isArray(book.isbn)) {
    const cleanQuery = queries[0].replace(/[-\s]/g, '');
    if (book.isbn.some(isbn => isbn.replace(/[-\s]/g, '') === cleanQuery)) {
      return 10000; // Return immediately for exact ISBN match
    }
  }

  // Check if book already exists in local library (penalize duplicates)
  const isInLocalLibrary = localBooks.some(localBook =>
    (book.isbn && localBook.isbn && localBook.isbn === book.isbn[0]) ||
    (book.key && localBook.olid && localBook.olid === book.key.split('/').pop())
  );

  if (isInLocalLibrary) {
    relevanceScore -= 500; // Penalize books already in library
  }

  for (const query of queries) {
    const lowerQuery = query.toLowerCase();
    const queryWords = lowerQuery.split(/\s+/).filter(w => w.length > 1);

    // Enhanced title matching with fuzzy support
    if (title === lowerQuery) {
      relevanceScore += 5000; // Exact match
    } else if (title.startsWith(lowerQuery)) {
      relevanceScore += 2500; // Starts with
    } else if (title.includes(lowerQuery)) {
      relevanceScore += 1200; // Contains
    } else {
      // Fuzzy title matching
      const titleSimilarity = getEnhancedStringSimilarity(title, lowerQuery);
      if (titleSimilarity > 0.8) {
        relevanceScore += Math.round(titleSimilarity * 1000);
      } else if (titleSimilarity > 0.6) {
        relevanceScore += Math.round(titleSimilarity * 500);
      }
    }

    // Enhanced word-by-word matching
    let matchedWords = 0;
    let partialMatches = 0;
    const titleWords = title.split(/\s+/).filter(w => w.length > 1);

    for (const queryWord of queryWords) {
      let wordMatched = false;
      let bestSimilarity = 0;

      for (const titleWord of titleWords) {
        if (titleWord === queryWord) {
          relevanceScore += 150; // Exact word match (increased from 100)
          matchedWords++;
          wordMatched = true;
          break;
        } else {
          const similarity = getEnhancedStringSimilarity(queryWord, titleWord);
          bestSimilarity = Math.max(bestSimilarity, similarity);
        }
      }

      // Fuzzy word matching with threshold
      if (!wordMatched && bestSimilarity > 0.7) {
        relevanceScore += Math.round(bestSimilarity * 80); // Fuzzy word match
        partialMatches++;
        wordMatched = true;
      }

      // Enhanced author matching
      if (!wordMatched) {
        if (authors.includes(queryWord)) {
          relevanceScore += 100; // Exact author match (increased from 80)
          matchedWords++;
        } else {
          // Fuzzy author matching
          const authorWords = authors.split(/\s+/).filter(w => w.length > 1);
          for (const authorWord of authorWords) {
            const similarity = getEnhancedStringSimilarity(queryWord, authorWord);
            if (similarity > 0.8) {
              relevanceScore += Math.round(similarity * 60);
              matchedWords++;
              break;
            }
          }
        }
      }
    }

    // Enhanced bonus for matching multiple words
    if (queryWords.length > 1) {
      const exactMatchRatio = matchedWords / queryWords.length;
      const totalMatchRatio = (matchedWords + partialMatches) / queryWords.length;

      relevanceScore += Math.round(exactMatchRatio * 300); // Exact matches
      relevanceScore += Math.round(totalMatchRatio * 150); // Including partial matches

      // Bonus for matching most/all words
      if (exactMatchRatio >= 0.8) {
        relevanceScore += 200; // Most words matched exactly
      }
      if (totalMatchRatio >= 0.9) {
        relevanceScore += 100; // Almost all words matched
      }
    }
  }

  // COVER IMAGE PRIORITIZATION - New feature based on user request
  if (book.cover_i || book.cover_edition_key) {
    relevanceScore += 25; // Significant boost for books with cover images

    // Additional boost for higher quality covers (cover_i indicates OpenLibrary has the image)
    if (book.cover_i) {
      relevanceScore += 15; // Extra boost for books with cover_i
    }
  }

  // Enhanced metadata scoring
  if (book.first_publish_year) {
    // Prefer more recent books (within reason)
    const currentYear = new Date().getFullYear();
    const bookAge = currentYear - book.first_publish_year;

    if (bookAge <= 5) {
      relevanceScore += 15; // Very recent books
    } else if (bookAge <= 20) {
      relevanceScore += 10; // Recent books
    } else if (book.first_publish_year > 1950) {
      relevanceScore += 5; // Not too old
    }
  }

  // Bonus for books with more metadata (indicates quality record)
  let metadataScore = 0;
  if (book.author_name && book.author_name.length > 0) metadataScore += 3;
  if (book.isbn && book.isbn.length > 0) metadataScore += 3;
  if (book.publisher && book.publisher.length > 0) metadataScore += 2;
  if (book.subject && book.subject.length > 0) metadataScore += 2;
  if (book.language && book.language.length > 0) metadataScore += 1;

  relevanceScore += metadataScore;

  // Slight preference for books with multiple editions (popular books)
  if (book.edition_count && book.edition_count > 1) {
    relevanceScore += Math.min(book.edition_count * 2, 20); // Cap at 20 points
  }

  return Math.max(relevanceScore, 0); // Ensure non-negative score
};

/**
 * Search for books by ISBN using OpenLibrary's books API
 * @param isbn - The ISBN to search for
 * @returns Promise of book search results
 */
const searchBooksByISBN = async (isbn: string): Promise<BookSearchResult[]> => {
  const cleanISBN = isbn.replace(/[-\s]/g, '');
  const url = `https://openlibrary.org/api/books?bibkeys=ISBN:${cleanISBN}&format=json&jscmd=data`;

  const response = await axios.get(url, {
    timeout: 25000, // Increased timeout for ISBN searches
    headers: {
      'User-Agent': 'Noti/1.0 (https://github.com/noti-app/noti)'
    }
  });

  const data = response.data;
  const isbnKey = `ISBN:${cleanISBN}`;
  const books: BookSearchResult[] = [];

  if (data[isbnKey]) {
    const book = data[isbnKey];
    const result: BookSearchResult = {
      title: book.title,
      author_name: book.authors ? book.authors.map((a: any) => a.name) : [],
      cover_i: book.cover ? parseInt(book.cover.medium.replace('M.jpg', '').split('/').pop()) : undefined,
      first_publish_year: book.publish_date ? parseInt(book.publish_date.split(', ').pop()) : undefined,
      isbn: book.identifiers?.isbn_13 || book.identifiers?.isbn_10 || [cleanISBN],
      publisher: book.publishers ? book.publishers.map((p: any) => p.name) : [],
      language: book.languages ? book.languages.map((l: any) => convertLanguageCode(l.key.replace('/languages/', ''))) : [],
      cover_url: book.cover ? book.cover.medium : undefined,
      isbn_primary: cleanISBN,
      relevanceScore: 10000 // Highest score for exact ISBN match
    };
    books.push(result);
  }

  return books;
};

// Search books using OpenLibrary API with enhanced algorithm
export const searchBooksOnline = async (
  query: string,
  limit: number = 10,
  localBooks: Book[] = []
): Promise<BookSearchResult[]> => {
  try {
    // Ensure limit doesn't exceed 10
    const searchLimit = Math.min(limit, 10);

    // Early return for very short queries that are likely to cause issues
    if (query.trim().length < 2) {
      console.log('Query too short, skipping search');
      return [];
    }

    // Check cache first (include localBooks count in cache key for deduplication)
    const cacheKey = `${query}_${searchLimit}_${localBooks.length}`;
    const cachedResults = searchCache.get<BookSearchResult[]>(cacheKey);
    
    if (cachedResults) {
      console.log('Returning cached results for:', query);
      return cachedResults;
    }

    const isISBNQuery = isISBN(query);
    let books: BookSearchResult[] = [];

    if (isISBNQuery) {
      // Use ISBN-specific search
      books = await searchBooksByISBN(query);
      // Apply enhanced scoring even for ISBN results
      books = books.map(book => ({
        ...book,
        relevanceScore: calculateEnhancedRelevanceScore(book, [query], isISBNQuery, localBooks)
      }));
    } else {
      // Preprocess query for better fuzzy matching
      const queryVariations = preprocessQuery(query);
      const primaryQuery = queryVariations[0];

      // Use regular search with reduced initial limit for better performance
      const encodedQuery = encodeURIComponent(primaryQuery);
      const url = `https://openlibrary.org/search.json?q=${encodedQuery}&limit=25&fields=title,author_name,isbn,cover_i,cover_edition_key,first_publish_year,key,subject,publisher,edition_count,language`;

      const response = await axios.get(url, {
        timeout: 25000, // Increased timeout
        headers: {
          'User-Agent': 'Noti/1.0 (https://github.com/noti-app/noti)'
        }
      });

      // Process and score results with enhanced algorithm
      const scoredBooks = response.data.docs.map((book: OpenLibrarySearchResult) => {
        const olid = book.key ? book.key.split('/').pop() : undefined;

        const result: BookSearchResult = {
          ...book,
          olid,
          cover_url: book.cover_i ? getOpenLibraryCoverUrl(book.cover_i, 'M') : undefined,
          genres: book.subject ? (() => {
            if (!book.subject || book.subject.length === 0) return null;
            const firstGenre = book.subject[0].trim();
            if (!firstGenre) return null;
            const firstWord = firstGenre.split(/\s+/)[0];
            return firstWord.charAt(0).toUpperCase() + firstWord.slice(1).toLowerCase();
          })() : undefined,
          isbn_primary: book.isbn ? book.isbn[0] : undefined,
          relevanceScore: calculateEnhancedRelevanceScore(book, queryVariations, isISBNQuery, localBooks)
        };

        return result;
      });

      // Sort by relevance score and take top results
      books = scoredBooks
        .filter(book => book.relevanceScore && book.relevanceScore > 0) // Only include books with positive relevance
        .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
        .slice(0, searchLimit);
    }

    // Cache the results using node-cache (handles TTL automatically)
    searchCache.set(cacheKey, books);

    console.log(`Enhanced search completed for "${query}": ${books.length} results (${books.filter(b => b.cover_i || b.cover_edition_key).length} with covers)`);
    return books;

  } catch (error) {
    console.error('Error searching books online:', error);

    // Return empty array instead of throwing error to allow graceful degradation
    console.log('Returning empty results due to error');
    return [];
  }
};

// Get detailed book information from OpenLibrary
export const getBookDetailsFromOpenLibrary = async (olid: string): Promise<Partial<Book>> => {
  try {
    // First, get work details
    const workUrl = `https://openlibrary.org/works/${olid}.json`;
    const workResponse = await axios.get<OpenLibraryWorkDetails>(workUrl, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Noti/1.0 (https://github.com/noti-app/noti)'
      }
    });

    const work = workResponse.data;
    let authorNames: string[] = [];
    let description = '';
    let genres = '';

    // Get author names with retry logic and increased timeout
    if (work.authors && work.authors.length > 0) {
      const authorPromises = work.authors.map(async (author) => {
        const authorUrl = `https://openlibrary.org${author.author.key}.json`;

        // Try with retry logic
        for (let attempt = 1; attempt <= 2; attempt++) {
          try {
            const authorResponse = await axios.get<OpenLibraryAuthorDetails>(authorUrl, {
              timeout: 10000, // Increased from 5000ms to 10000ms
              headers: {
                'User-Agent': 'Noti/1.0 (https://github.com/noti-app/noti)'
              }
            });
            return authorResponse.data.name;
          } catch (error) {
            if (attempt === 1) {
              const isTimeout = isTimeoutError(error);
              const errorType = isTimeout ? 'timeout' : 'network error';
              console.warn(`Failed to fetch author details (${errorType}) on attempt ${attempt}, retrying:`, error);
              // Wait 1 second before retry
              await new Promise(resolve => setTimeout(resolve, 1000));
            } else {
              const isTimeout = isTimeoutError(error);
              const errorType = isTimeout ? 'timeout' : 'network error';
              console.warn(`Failed to fetch author details (${errorType}) after ${attempt} attempts:`, error);
              return null;
            }
          }
        }
        return null;
      });

      try {
        const authors = await Promise.all(authorPromises);
        authorNames = authors.filter(name => name !== null) as string[];
      } catch (error) {
        console.warn('Failed to fetch some author details, continuing with available data:', error);
        // Continue execution even if some authors failed
      }

      // If no author names were found, try to extract basic names from author keys as fallback
      if (authorNames.length === 0 && work.authors && work.authors.length > 0) {
        console.log('Using author keys as fallback for author names');
        authorNames = work.authors.map(author => {
          // Extract author name from key like /authors/OL2745568A -> OL2745568A
          const authorKey = author.author.key.split('/').pop() || 'Unknown Author';
          return authorKey.replace(/^OL|A$/g, '') || 'Unknown Author'; // Remove OL prefix and A suffix if present
        });
      }
    }

    // Process description
    if (work.description) {
      if (typeof work.description === 'string') {
        description = work.description;
      } else if (work.description.value) {
        description = work.description.value;
      }
    }

    // Process genres/subjects
    if (work.subjects && work.subjects.length > 0) {
      const processedGenre = (() => {
        if (!work.subjects || work.subjects.length === 0) return null;
        const firstGenre = work.subjects[0].trim();
        if (!firstGenre) return null;
        const firstWord = firstGenre.split(/\s+/)[0];
        return firstWord.charAt(0).toUpperCase() + firstWord.slice(1).toLowerCase();
      })();
      genres = processedGenre || '';
    }

    // Try to get edition details for more specific information
    let isbn = '';
    let pageCount: number | null = null;
    let publicationDate = '';
    let language = '';
    let coverUrl = '';

    try {
      // Get editions for this work
      const editionsUrl = `https://openlibrary.org/works/${olid}/editions.json`;
      const editionsResponse = await axios.get(editionsUrl, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Noti/1.0 (https://github.com/noti-app/noti)'
        }
      });

      const editions = editionsResponse.data.entries;
      if (editions && editions.length > 0) {
        // Find the best edition (preferably with ISBN and page count)
        const bestEdition = editions.find((ed: OpenLibraryEditionDetails) =>
          (ed.isbn_13 && ed.isbn_13.length > 0) || (ed.isbn_10 && ed.isbn_10.length > 0)
        ) || editions[0];

        if (bestEdition) {
          // Get ISBN (prefer ISBN-13)
          if (bestEdition.isbn_13 && bestEdition.isbn_13.length > 0) {
            isbn = bestEdition.isbn_13[0];
          } else if (bestEdition.isbn_10 && bestEdition.isbn_10.length > 0) {
            isbn = bestEdition.isbn_10[0];
          }

          // Get page count
          if (bestEdition.number_of_pages) {
            pageCount = bestEdition.number_of_pages;
          }

          // Get publication date
          if (bestEdition.publish_date) {
            publicationDate = extractPublicationYear(bestEdition.publish_date);
          } else if (work.first_publish_date) {
            publicationDate = extractPublicationYear(work.first_publish_date);
          }

          // Get language
          if (bestEdition.languages && bestEdition.languages.length > 0) {
            const langKey = bestEdition.languages[0].key;
            const rawLanguageCode = langKey.replace('/languages/', '');
            // Convert language code to human-readable name
            language = convertLanguageCode(rawLanguageCode);
          }

          // Get cover
          if (bestEdition.covers && bestEdition.covers.length > 0) {
            coverUrl = getOpenLibraryCoverUrl(bestEdition.covers[0]);
          } else if (work.covers && work.covers.length > 0) {
            coverUrl = getOpenLibraryCoverUrl(work.covers[0]);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to fetch edition details:', error);      // Use work-level data as fallback
      if (work.first_publish_date) {
        publicationDate = extractPublicationYear(work.first_publish_date);
      }
      if (work.covers && work.covers.length > 0) {
        coverUrl = getOpenLibraryCoverUrl(work.covers[0]);
      }
    }

    return {
      title: work.title,
      author: authorNames.length > 0 ? authorNames.join(', ') : null,
      isbn: isbn || null,
      description: description || null,
      genres: genres || null,
      page_count: pageCount,
      publication_date: extractPublicationYear(publicationDate),
      language: language || null,
      olid,
      cover_url: coverUrl || null
    };

  } catch (error) {
    console.error('Error getting book details from OpenLibrary:', error);
    throw new Error('Failed to get book details from OpenLibrary');
  }
};

// Helper function to validate and transform Partial<Book> to Book
const validateAndTransformBookData = (bookData: Partial<Book>): Book => {
  // Validate required fields
  if (!bookData.title || typeof bookData.title !== 'string' || bookData.title.trim() === '') {
    throw new Error('Book title is required and must be a non-empty string');
  }

  // Create a complete Book object with proper defaults
  const book: Book = {
    title: bookData.title.trim(),
    author: bookData.author || null,
    isbn: bookData.isbn || null,
    cover_url: bookData.cover_url || null,
    publication_date: bookData.publication_date || null,
    description: bookData.description || null,
    page_count: bookData.page_count || null,
    current_page: bookData.current_page || null,
    rating: bookData.rating || null,
    language: bookData.language || null,
    genres: bookData.genres || null,
    olid: bookData.olid || null,
    status: bookData.status || 'unread',
    custom_fields: bookData.custom_fields || null,
    // Note: id, created_at, updated_at will be set by the database
  };

  // Additional validation for specific fields
  if (book.rating !== null && (book.rating < 0 || book.rating > 5)) {
    throw new Error('Rating must be between 0 and 5');
  }

  if (book.page_count !== null && book.page_count < 0) {
    throw new Error('Page count must be a positive number');
  }

  if (book.current_page !== null && book.current_page < 0) {
    throw new Error('Current page must be a positive number');
  }

  if (book.current_page !== null && book.page_count !== null && book.current_page > book.page_count) {
    throw new Error('Current page cannot be greater than total page count');
  }

  return book;
};

// Helper function to check if error is a timeout error
const isTimeoutError = (error: any): boolean => {
  return error?.code === 'ECONNABORTED' ||
         error?.message?.includes('timeout') ||
         error?.code === 'ETIMEDOUT';
};

/**
 * Extracts only the year part from a publication date string.
 * OpenLibrary dates can come in various formats like "January 2005", "2010", "2008-03-01", etc.
 * This function ensures we only store and use the year part.
 * Also handles BCE years in various formats like "32 BCE", "32 BC", "-32"
 *
 * @param dateString - The publication date string from any source
 * @returns The year as a string (negative for BCE), or null if no valid year can be extracted
 */
const extractPublicationYear = (dateString: string | null | undefined): string | null => {
  if (!dateString) return null;

  // Check for BCE/BC years
  const bceMatch = dateString.match(/(\d+)\s*(?:BCE|BC)(?:\.|)/i);
  if (bceMatch && bceMatch[1]) {
    // Convert BCE year to negative number
    return `-${bceMatch[1]}`;
  }

  // Check for already negative years (already in numeric BCE format)
  const negativeYearMatch = dateString.match(/^-(\d+)$/);
  if (negativeYearMatch) {
    return dateString; // Already in the right format
  }

  // Modern years: First try to extract a 4-digit year pattern
  const yearMatch = dateString.match(/\b\d{4}\b/);
  if (yearMatch && yearMatch[0]) {
    const year = parseInt(yearMatch[0], 10);
    const currentYear = new Date().getFullYear();

    // Reject future years
    if (year > currentYear) {
      return null;
    }

    return yearMatch[0];
  }

  // If we couldn't find a year with regex, try to parse it as a date
  try {
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date.getFullYear().toString();
    }
  } catch (e) {
    // Parsing failed, continue to fallback
  }

  return null;
};

// Helper function to check if a string is a data URL (base64 image)
const isDataUrl = (url: string): boolean => {
  return url.startsWith('data:image/');
};

// Helper function to convert data URL to buffer
const dataUrlToBuffer = (dataUrl: string): Buffer => {
  const base64Data = dataUrl.split(',')[1];
  return Buffer.from(base64Data, 'base64');
};

// Check and download missing covers for books
export const checkAndDownloadMissingCovers = async (): Promise<void> => {
  try {
    console.log('Checking for books with missing cover files...');

    // Get all books that have a cover_url but no cover in media_files
    const query = `
      SELECT b.id, b.title, b.cover_url
      FROM books b
      LEFT JOIN media_files m ON b.id = m.book_id AND m.is_cover = 1
      WHERE b.cover_url IS NOT NULL
      AND b.cover_url != ''
      AND m.id IS NULL
    `;

    const booksWithMissingCovers = await dbAll<{id: number, title: string, cover_url: string}>(query, []);

    if (booksWithMissingCovers.length === 0) {
      console.log('No books with missing covers found.');
      return;
    }

    console.log(`Found ${booksWithMissingCovers.length} books with missing cover files. Starting downloads...`);

    let successCount = 0;
    let failureCount = 0;

    // Process books in batches to avoid overwhelming the system
    const batchSize = 3;
    for (let i = 0; i < booksWithMissingCovers.length; i += batchSize) {
      const batch = booksWithMissingCovers.slice(i, i + batchSize);

      const downloadPromises = batch.map(async (book) => {
        try {
          console.log(`Downloading cover for "${book.title}"...`);

          // Check if it's a data URL (from manual upload)
          if (isDataUrl(book.cover_url)) {
            const coverBuffer = dataUrlToBuffer(book.cover_url);
            await saveBookCover(book.id, coverBuffer, 'cover.jpg');
            console.log(`✓ Saved cover for "${book.title}" from data URL`);
          } else {
            // Download from URL
            const coverData = await downloadCoverImageData(book.cover_url);
            await saveBookCover(book.id, coverData);
            console.log(`✓ Downloaded and saved cover for "${book.title}"`);
          }

          successCount++;
        } catch (error) {
          console.error(`✗ Failed to download cover for "${book.title}":`, error);
          failureCount++;
        }
      });

      await Promise.allSettled(downloadPromises);

      // Small delay between batches to avoid rate limiting
      if (i + batchSize < booksWithMissingCovers.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`Cover download check completed: ${successCount} successful, ${failureCount} failed`);
  } catch (error) {
    console.error('Error in checkAndDownloadMissingCovers:', error);
  }
};

// Create a book with validation and cover image download (enhanced for data URLs)
export const createBookWithValidation = async (bookData: Partial<Book>, downloadCover: boolean = true): Promise<Book> => {
  const validatedBookData = validateAndTransformBookData(bookData);

  // Download cover image if requested and URL is provided
  if (downloadCover && validatedBookData.cover_url && !isDataUrl(validatedBookData.cover_url)) {
    try {
      console.log(`Attempting to download cover from: ${validatedBookData.cover_url}`);
      const imageData = await downloadCoverImageData(validatedBookData.cover_url);
      const base64Image = `data:image/jpeg;base64,${imageData.toString('base64')}`;
      validatedBookData.cover_url = base64Image;
      console.log('Cover image downloaded and converted to base64.');
    } catch (error) {
      console.error('Failed to download or process cover image, proceeding without it:', error);
      // Optionally, set cover_url to null or a placeholder if download fails
      validatedBookData.cover_url = null;
    }
  } else if (validatedBookData.cover_url && isDataUrl(validatedBookData.cover_url)) {
    console.log('Cover image is already a data URL, no download needed.');
  } else {
    console.log('No cover URL provided or downloadCover is false.');
  }

  const createdBook = await createBook(validatedBookData);

  // Notify database hooks about book creation
  if (createdBook.id) {
    notifyBookChange('create', createdBook.id, {
      title: createdBook.title,
      author: createdBook.author,
      isbn: createdBook.isbn
    });
  }

  // CRITICAL FIX: Process cover immediately after book creation to prevent timing issues
  if (createdBook.id && createdBook.cover_url && isDataUrl(createdBook.cover_url)) {
    try {
      console.log(`Processing cover immediately for book "${createdBook.title}" (ID: ${createdBook.id})`);
      const coverBuffer = dataUrlToBuffer(createdBook.cover_url);
      await saveBookCover(createdBook.id, coverBuffer, 'cover.jpg');
      console.log(`✓ Cover processed and saved immediately for book "${createdBook.title}"`);

      // OPTIMIZATION: Clear base64 data URL from database to prevent sync manifest bloat
      // The cover is now stored in media_files table, so we don't need the base64 data in cover_url
      await updateBook(createdBook.id, { cover_url: null });
      console.log(`✓ Cleared base64 data URL from database for book "${createdBook.title}" to optimize sync`);
    } catch (coverError) {
      console.error(`Failed to process cover immediately for book "${createdBook.title}":`, coverError);
      // Don't fail book creation if cover processing fails
    }
  }

  // Ensure the Books root folder exists and create a folder for the book
  try {
    // 🐛 DUPLICATE FOLDER BUG DEBUG: Enhanced book folder creation logging
    console.log(`\n📚 BOOK FOLDER CREATION START`);
    console.log(`   📖 Book: "${createdBook.title}" (ID: ${createdBook.id})`);
    
    const booksRootFolder = await ensureBooksRootFolder();
    if (!booksRootFolder || !booksRootFolder.id) {
      throw new Error('Failed to get or create Books root folder ID.');
    }

    console.log(`   📁 Books root folder: "${booksRootFolder.name}" (ID: ${booksRootFolder.id})`);

    const bookTitle = createdBook.title;

    // Since we now use book_id for linking, we can use clean folder names
    // Use Unicode-safe sanitization that preserves diacritics and special characters
    const folderName = sanitizeBookTitle(bookTitle);
    
    console.log(`   🏷️ Sanitized folder name: "${folderName}"`);
    console.log(`   🔗 Creating folder with:`);
    console.log(`     - name: "${folderName}"`);
    console.log(`     - parent_id: ${booksRootFolder.id}`);
    console.log(`     - book_id: ${createdBook.id}`);

    await createFolderWithValidation({
      name: folderName,
      parent_id: booksRootFolder.id,
      book_id: createdBook.id,
    });
    
    console.log(`✅ Successfully created folder "${folderName}" for book ID ${createdBook.id} under Books folder ID ${booksRootFolder.id}`);
    console.log(`📚 BOOK FOLDER CREATION END\n`);
  } catch (folderError) {
    // Log the error but don't let it prevent book creation
    console.error(`❌ Error creating folder for book (non-critical):`, folderError);
    console.log(`📚 BOOK FOLDER CREATION END (error)\n`);
  }

  return createdBook;
};

// Update a book with validation
export const updateBookWithValidation = async (id: number, bookUpdates: Partial<Book>): Promise<Book> => {
  // Ensure id is a number
  if (typeof id !== 'number') {
    throw new Error('Book ID is required and must be a number');
  }

  try {
    const result = await updateBook(id, bookUpdates);

    // Notify database hooks about book update
    notifyBookChange('update', id, {
      title: result.title,
      author: result.author,
      isbn: result.isbn,
      updatedFields: Object.keys(bookUpdates)
    });

    return result;
  } catch (error: any) {
    console.error('Error in updateBookWithValidation:', error);
    throw new Error(`Failed to update book ${id}: ${error.message}`);
  }
};

export const deleteBookAndHandleFolder = async (bookId: number): Promise<{ success: boolean; id: number }> => {
  if (typeof bookId !== 'number' || bookId <= 0) {
    throw new Error('Invalid Book ID provided for deletion.');
  }

  console.log(`Starting deletion process for book ID: ${bookId}`);

  try {
    const bookFolder = await getBookFolder(bookId);

    if (bookFolder && bookFolder.id) {
      console.log(`Book folder found for book ${bookId}: ID ${bookFolder.id}, Name: "${bookFolder.name}"`);

      const notesInFolder = await getNotesByFolderId(bookFolder.id);
      console.log(`Found ${notesInFolder.length} notes in folder ${bookFolder.id}.`);

      if (notesInFolder.length > 0) {
        console.log(`Folder contains notes. Updating book_id to null for notes in folder ${bookFolder.id} that are linked to book ${bookId}.`);
        for (const note of notesInFolder) {
          if (note.id && note.book_id === bookId) { // Ensure we only update notes linked to this specific book
            try {
              await updateNote(note.id, { book_id: null });
              console.log(`Successfully set book_id to null for note ${note.id}`);
            } catch (noteUpdateError) {
              console.error(`Failed to update book_id for note ${note.id}:`, noteUpdateError);
              // Log and continue, as this might not be critical enough to stop the whole process
            }
          }
        }

        console.log(`Attempting to move folder ${bookFolder.id} ("${bookFolder.name}") to root and unlink from book.`);
        try {
          await updateFolderWithValidation(bookFolder.id, { parent_id: null, book_id: null });
          console.log(`Folder ${bookFolder.id} ("${bookFolder.name}") successfully moved to root and unlinked from book.`);
        } catch (folderMoveError) {
          console.error(`Failed to move folder ${bookFolder.id} to root:`, folderMoveError);
          // This is a critical part of the new requirement. If it fails, rethrow.
          throw new Error(`Failed to move book folder to root: ${folderMoveError instanceof Error ? folderMoveError.message : String(folderMoveError)}`);
        }
      } else {
        console.log(`Folder contains no notes. Deleting empty folder ${bookFolder.id} ("${bookFolder.name}").`);
        try {
          await deleteFolderFromDb(bookFolder.id);
          console.log(`Empty folder ${bookFolder.id} ("${bookFolder.name}") successfully deleted.`);
        } catch (folderDeleteError) {
          console.error(`Failed to delete empty folder ${bookFolder.id}:`, folderDeleteError);
          // This is a critical part of the new requirement. If it fails, rethrow.
          throw new Error(`Failed to delete empty book folder: ${folderDeleteError instanceof Error ? folderDeleteError.message : String(folderDeleteError)}`);
        }
      }
    } else {
      console.log(`No specific folder found for book ${bookId}, or folder has no ID. Proceeding with book deletion from DB only.`);
    }

    console.log(`Proceeding to delete book entry ${bookId} from database.`);
    // Get book title before deletion for the event
    const bookToDelete = await getBookById(bookId);
    const bookTitle = bookToDelete?.title || `Book ${bookId}`;

    // deleteBookFromDb also handles deleting media files and notes directly linked via book_id.
    // Notes in the book's folder should have had their book_id nulled, so they won't be deleted by this.
    const deletionResult = await deleteBookFromDb(bookId);

    // Notify database hooks about book deletion
    if (deletionResult.success) {
      notifyBookChange('delete', bookId, {
        title: bookToDelete?.title,
        author: bookToDelete?.author,
        isbn: bookToDelete?.isbn
      });
    }

    console.log(`Book ${bookId} deletion from DB successful: ${deletionResult.success}`);
    return deletionResult;

  } catch (error) {
    console.error(`Error in deleteBookAndHandleFolder for book ID ${bookId}:`, error);
    if (error instanceof Error) {
        throw new Error(`Failed to delete book and handle folder for ID ${bookId}: ${error.message}`);
    }
    throw new Error(`An unknown error occurred while deleting book and handling folder for ID ${bookId}`);
  }
};

// Get books with metadata (enhanced for frontend)
export const getBooksWithMetadata = async (): Promise<BookWithNoteCount[]> => {
  try {
    const books = await getAllBooksWithNoteCounts();

    if (books.length === 0) {
      return books;
    }

    // Get all cover files for all books in a single query to avoid N+1 problem
    const bookIds = books.map(book => book.id).filter(id => id !== undefined) as number[];
    const allCovers = new Map<number, string>();

    if (bookIds.length > 0) {
      try {
        // Single query to get all covers
        const coverQuery = `
          SELECT book_id, file_path
          FROM media_files
          WHERE book_id IN (${bookIds.map(() => '?').join(',')})
          AND is_cover = 1
          ORDER BY book_id, created_at DESC
        `;

        const coverResults = await dbAll<{book_id: number, file_path: string}>(coverQuery, bookIds);

        // Create a map of book_id -> cover_url, keeping only the most recent cover per book
        const seenBookIds = new Set<number>();
        for (const cover of coverResults) {
          if (!seenBookIds.has(cover.book_id)) {
            allCovers.set(cover.book_id, filePathToMediaUrl(cover.file_path));
            seenBookIds.add(cover.book_id);
          }
        }
      } catch (coverError) {
        console.warn('Failed to load cover data:', coverError);
        // Continue without covers rather than failing completely
      }
    }

    // Process books with cover data from the single query
    const booksWithCovers = books.map(book => {
      const coverMediaUrl = book.id ? allCovers.get(book.id) || null : null;

      return {
        ...book,
        // Add computed fields that the frontend might need
        addedDate: book.created_at ? new Date(book.created_at) : new Date(),
        // Ensure rating is within valid range
        rating: book.rating && book.rating >= 0 && book.rating <= 5 ? book.rating : undefined,
        // Add cover media URL for frontend use
        cover_media_url: coverMediaUrl
      };
    });

    return booksWithCovers;
  } catch (error: any) {
    console.error('Error in getBooksWithMetadata:', error);
    throw new Error(`Failed to get books with metadata: ${error.message}`);
  }
};

// Search books both locally and online with enhanced scoring
export const searchBooksHybrid = async (
  searchTerm: string,
  includeOnline: boolean = true,
  onlineLimit: number = 10
): Promise<{
  localResults: Book[];
  onlineResults: BookSearchResult[];
}> => {
  try {
    // Search local database first
    const localResults = await searchBooksInDb(searchTerm);

    let onlineResults: BookSearchResult[] = [];

    if (includeOnline) {
      try {
        // Pass local books to online search for enhanced scoring and deduplication
        onlineResults = await searchBooksOnline(searchTerm, onlineLimit, localResults);

        // Log search results for debugging
        const booksWithCovers = onlineResults.filter(b => b.cover_i || b.cover_edition_key).length;
        const averageScore = onlineResults.length > 0
          ? Math.round(onlineResults.reduce((sum, book) => sum + (book.relevanceScore || 0), 0) / onlineResults.length)
          : 0;

        console.log(`Hybrid search for "${searchTerm}": ${localResults.length} local, ${onlineResults.length} online (${booksWithCovers} with covers, avg score: ${averageScore})`);
      } catch (onlineError) {
        console.warn('Online search failed:', onlineError);
        // Continue with local results only
      }
    }

    return {
      localResults,
      onlineResults
    };
  } catch (error: any) {
    console.error('Error in searchBooksHybrid:', error);
    throw new Error(`Failed to search books: ${error.message}`);
  }
};

// Add book from OpenLibrary search result
export const addBookFromOpenLibrary = async (searchResult: BookSearchResult): Promise<Book> => {
  try {
    // Check if book already exists by ISBN or OLID
    if (searchResult.isbn_primary) {
      const existingByIsbn = await getBookByIsbn(searchResult.isbn_primary);
      if (existingByIsbn) {
        throw new Error('Book with this ISBN already exists in your library');
      }
    }

    if (searchResult.olid) {
      const existingByOlid = await getBookByOlid(searchResult.olid);
      if (existingByOlid) {
        throw new Error('Book with this OpenLibrary ID already exists in your library');
      }
    }

    // Get detailed information if we have an OLID
    let bookData: Partial<Book>;

    if (searchResult.olid) {
      try {
        bookData = await getBookDetailsFromOpenLibrary(searchResult.olid);

        // Check if this is a custom cover that should be preserved
        const hasCustomCover = searchResult.cover_url &&
          (searchResult.cover_url.startsWith('data:') ||
           searchResult.hasCustomCover ||
           searchResult.userModifiedCover ||
           searchResult.preventCoverOverride);

        if (hasCustomCover) {
          // Preserve user's custom cover
          bookData.cover_url = searchResult.cover_url;
        } else if (searchResult.cover_i) {
          // Use high-quality OpenLibrary cover
          bookData.cover_url = getOpenLibraryCoverUrl(searchResult.cover_i, 'L');
        } else if (searchResult.cover_url) {
          // Fallback to original cover_url if no cover_i available
          bookData.cover_url = searchResult.cover_url;
        }

        // CRITICAL FIX: Preserve user-edited fields from search result
        // If searchResult has specific fields that differ from API data, preserve them
        if (searchResult.title && searchResult.title !== bookData.title) {
          bookData.title = searchResult.title;
        }
        if (searchResult.author_name && searchResult.author_name.length > 0) {
          bookData.author = searchResult.author_name.join(', ');
        }
        if (searchResult.language && searchResult.language.length > 0) {
          // Preserve user-edited language field
          bookData.language = searchResult.language[0];
        }
        if (searchResult.genres) {
          bookData.genres = searchResult.genres;
        }
        if (searchResult.first_publish_year) {
          bookData.publication_date = extractPublicationYear(searchResult.first_publish_year.toString());
        }
      } catch (detailError) {
        const isTimeout = isTimeoutError(detailError);
        const errorMessage = isTimeout
          ? 'OpenLibrary API timeout - using basic book information'
          : 'Failed to get detailed info, using search result data';

        console.warn(`${errorMessage}:`, detailError);

        // Check if this is a custom cover that should be preserved (fallback case)
        const hasCustomCover = searchResult.cover_url &&
          (searchResult.cover_url.startsWith('data:') ||
           searchResult.hasCustomCover ||
           searchResult.userModifiedCover ||
           searchResult.preventCoverOverride);

        let coverUrl: string | null = null;
        if (hasCustomCover) {
          // Preserve user's custom cover
          coverUrl = searchResult.cover_url;
        } else if (searchResult.cover_i) {
          // Use high-quality OpenLibrary cover
          coverUrl = getOpenLibraryCoverUrl(searchResult.cover_i, 'L');
        } else {
          // Fallback to original cover_url
          coverUrl = searchResult.cover_url || null;
        }

        // Fallback to search result data with proper cover handling
        bookData = {
          title: searchResult.title,
          author: searchResult.author_name ? searchResult.author_name.join(', ') : null,
          isbn: searchResult.isbn_primary || null,
          olid: searchResult.olid,
          cover_url: coverUrl,
          genres: searchResult.genres || null,
          publication_date: searchResult.first_publish_year ? extractPublicationYear(searchResult.first_publish_year.toString()) : null,
          language: searchResult.language ? searchResult.language[0] : null
        };
      }
    } else {
      // Check if this is a custom cover that should be preserved (no OLID case)
      const hasCustomCover = searchResult.cover_url &&
        (searchResult.cover_url.startsWith('data:') ||
         searchResult.hasCustomCover ||
         searchResult.userModifiedCover ||
         searchResult.preventCoverOverride);

      let coverUrl: string | null = null;
      if (hasCustomCover) {
        // Preserve user's custom cover
        coverUrl = searchResult.cover_url;
      } else if (searchResult.cover_i) {
        // Use high-quality OpenLibrary cover
        coverUrl = getOpenLibraryCoverUrl(searchResult.cover_i, 'L');
      } else {
        // Fallback to original cover_url
        coverUrl = searchResult.cover_url || null;
      }

      // Use search result data directly with proper cover handling
      bookData = {
        title: searchResult.title,
        author: searchResult.author_name ? searchResult.author_name.join(', ') : null,
        isbn: searchResult.isbn_primary || null,
        cover_url: coverUrl,
        genres: searchResult.genres || null,
        publication_date: searchResult.first_publish_year ? extractPublicationYear(searchResult.first_publish_year.toString()) : null,
        language: searchResult.language ? searchResult.language[0] : null
      };
    }

    return await createBookWithValidation(bookData, true);
  } catch (error: any) {
    console.error('Error in addBookFromOpenLibrary:', error);
    throw new Error(`Failed to add book from OpenLibrary: ${error.message}`);
  }
};

// Function to ensure all existing books have folders
export const ensureFoldersForAllBooks = async (): Promise<{ created: number; errors: number; total: number }> => {
  try {
    // 🐛 DUPLICATE FOLDER BUG DEBUG: Enhanced folder ensuring logging
    console.log(`\n🔧 ENSURE FOLDERS FOR ALL BOOKS START`);
    console.log(`   ⏰ Timestamp: ${new Date().toISOString()}`);
    console.log(`   🎯 This function might be creating duplicate folders!`);

    // Get all books
    const allBooks = await getAllBooks();
    let created = 0;
    let errors = 0;

    console.log(`   📊 Found ${allBooks.length} books to check for folders`);

    for (const book of allBooks) {
      try {
        console.log(`\n   📖 Checking book: "${book.title}" (ID: ${book.id})`);
        
        // Check if book already has a folder
        const existingFolder = await getBookFolder(book.id!);

        if (existingFolder) {
          console.log(`     ✅ Book already has folder: "${existingFolder.name}" (ID: ${existingFolder.id})`);
          console.log(`     📁 Existing folder parent_id: ${existingFolder.parent_id}`);
          console.log(`     📁 Existing folder book_id: ${existingFolder.book_id}`);
          continue;
        }

        // Create folder for this book
        console.log(`     🚨 MISSING FOLDER - Creating folder for book "${book.title}"`);
        console.log(`     ⚠️ This could be the source of duplicate folders!`);

        const booksRootFolder = await ensureBooksRootFolder();
        if (!booksRootFolder || !booksRootFolder.id) {
          throw new Error('Failed to get or create Books root folder ID.');
        }

        console.log(`     📁 Books root folder: "${booksRootFolder.name}" (ID: ${booksRootFolder.id})`);

        // Generate clean folder name using Unicode-safe sanitization
        const folderName = sanitizeBookTitle(book.title);
        console.log(`     🏷️ Sanitized folder name: "${folderName}"`);

        console.log(`     🔗 Creating folder via ensureFoldersForAllBooks with:`);
        console.log(`       - name: "${folderName}"`);
        console.log(`       - parent_id: ${booksRootFolder.id}`);
        console.log(`       - book_id: ${book.id}`);

        await createFolderWithValidation({
          name: folderName,
          parent_id: booksRootFolder.id,
          book_id: book.id,
        });

        console.log(`     ✅ Created folder "${folderName}" for book ID ${book.id} via ensureFoldersForAllBooks`);
        created++;

      } catch (error) {
        console.error(`     ❌ Failed to create folder for book "${book.title}" (ID: ${book.id}):`, error);
        errors++;
      }
    }

    const result = { created, errors, total: allBooks.length };
    console.log(`\n   📊 ENSURE FOLDERS SUMMARY:`);
    console.log(`     - Created: ${created} folders`);
    console.log(`     - Errors: ${errors} errors`);
    console.log(`     - Total books: ${allBooks.length}`);
    console.log(`🔧 ENSURE FOLDERS FOR ALL BOOKS END\n`);
    return result;

  } catch (error) {
    console.error(`❌ Error in ensureFoldersForAllBooks:`, error);
    console.log(`🔧 ENSURE FOLDERS FOR ALL BOOKS END (error)\n`);
    throw new Error(`Failed to ensure folders for all books: ${error.message}`);
  }
};

// Function to get books without folders
export const getBooksWithoutFolders = async (): Promise<Book[]> => {
  try {
    const query = `
      SELECT b.*
      FROM books b
      LEFT JOIN folders f ON b.id = f.book_id
      WHERE f.id IS NULL
    `;

    const booksWithoutFolders = await dbAll<Book>(query, []);
    console.log(`Found ${booksWithoutFolders.length} books without folders`);
    return booksWithoutFolders;

  } catch (error) {
    console.error('Error getting books without folders:', error);
    throw new Error(`Failed to get books without folders: ${error.message}`);
  }
};

// OPTIMIZATION: Clean up base64 data URLs from database to prevent sync manifest bloat
export const cleanupBase64CoverUrls = async (): Promise<{ processed: number; errors: number }> => {
  try {
    console.log('🧹 Starting cleanup of base64 cover URLs to optimize sync manifest size...');

    // Find books with base64 data URLs in cover_url field
    const query = `SELECT id, title, cover_url FROM books WHERE cover_url LIKE 'data:%'`;
    const booksWithBase64 = await dbAll<{id: number, title: string, cover_url: string}>(query, []);

    console.log(`Found ${booksWithBase64.length} books with base64 cover URLs to clean up`);

    let processed = 0;
    let errors = 0;

    for (const book of booksWithBase64) {
      try {
        console.log(`Processing book "${book.title}" (ID: ${book.id})`);

        // Check if this book already has a cover in media_files
        const existingCover = await getBookCover(book.id);

        if (existingCover) {
          // Cover already exists in media_files, safe to clear base64 data
          await updateBook(book.id, { cover_url: null });
          console.log(`✓ Cleared base64 data for "${book.title}" (cover exists in media_files)`);
        } else {
          // No cover in media_files, process the base64 data first
          const coverBuffer = dataUrlToBuffer(book.cover_url);
          await saveBookCover(book.id, coverBuffer, 'cover.jpg');
          await updateBook(book.id, { cover_url: null });
          console.log(`✓ Processed and cleared base64 data for "${book.title}"`);
        }

        processed++;
      } catch (error) {
        console.error(`❌ Failed to process book "${book.title}" (ID: ${book.id}):`, error);
        errors++;
      }
    }

    console.log(`🧹 Cleanup complete: ${processed} processed, ${errors} errors`);
    return { processed, errors };

  } catch (error: any) {
    console.error('Error in cleanupBase64CoverUrls:', error);
    throw new Error(`Failed to cleanup base64 cover URLs: ${error.message}`);
  }
};

// Export all functions
export default {
  // Basic CRUD operations
  createBook: createBookWithValidation,
  getAllBooks,
  getAllBooksWithNoteCounts,
  getBooksWithMetadata,
  getBookById,
  getBookByIsbn,
  getBookByOlid,
  updateBook: updateBookWithValidation,
  deleteBook: deleteBookAndHandleFolder, // Use the new function
  getRecentBooks,

  // Search operations
  searchBooks: searchBooksInDb,
  searchBooksOnline,
  searchBooksHybrid,

  // OpenLibrary integration
  getBookDetailsFromOpenLibrary,
  addBookFromOpenLibrary,

  // Utility functions
  downloadCoverImageData,
  checkAndDownloadMissingCovers,
  ensureFoldersForAllBooks,
  getBooksWithoutFolders,
  getEnhancedStringSimilarity
};
